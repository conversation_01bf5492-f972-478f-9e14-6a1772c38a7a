/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./*.{js,html}"
  ],
  darkMode: ['class', '[data-theme="night"]'],
  theme: {
    extend: {
      colors: {
        // Romantic color palette from your existing CSS
        'primary-pink': '#ff6b9d',
        'secondary-pink': '#ffc3d8',
        'light-pink': '#fff0f5',
        'accent-purple': '#c44569',
        'soft-white': '#fefefe',
        'text-dark': '#2c2c2c',
        'text-light': '#666',
        // Additional romantic shades
        'romantic': {
          50: '#fff0f5',   // light-pink
          100: '#ffc3d8',  // secondary-pink
          200: '#ff9a9e',  // gradient color
          300: '#ff8fab',  // night primary-pink
          400: '#ff6b9d',  // primary-pink
          500: '#c44569',  // accent-purple
          600: '#8e44ad',  // night accent-purple
          700: '#d4a5c7',  // night secondary-pink
          800: '#2a1f3d',  // night light-pink
          900: '#1a1a2e',  // night soft-white
        },
        // Night theme colors
        'night': {
          'primary-pink': '#ff8fab',
          'secondary-pink': '#d4a5c7',
          'light-pink': '#2a1f3d',
          'accent-purple': '#8e44ad',
          'soft-white': '#1a1a2e',
          'text-dark': '#f8f8f8',
          'text-light': '#cccccc',
          'bg-start': '#2c1810',
          'bg-mid': '#3d2f47',
          'bg-end': '#4a3c5a',
        }
      },
      backgroundImage: {
        'romantic-gradient': 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
        'night-gradient': 'linear-gradient(135deg, #2c1810 0%, #3d2f47 50%, #4a3c5a 100%)',
        'card-gradient': 'linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 195, 216, 0.3))',
        'night-card-gradient': 'linear-gradient(145deg, rgba(42, 31, 61, 0.9), rgba(212, 165, 199, 0.1))',
      },
      boxShadow: {
        'soft': '0 10px 30px rgba(255, 107, 157, 0.2)',
        'hover': '0 15px 40px rgba(255, 107, 157, 0.3)',
      },
      fontFamily: {
        'dancing': ['Dancing Script', 'cursive'],
        'playfair': ['Playfair Display', 'serif'],
        'poppins': ['Poppins', 'sans-serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'sparkle': 'sparkleFloat 8s ease-in-out infinite',
        'heart-beat': 'heartbeat 1.5s ease-in-out infinite',
        'fade-in': 'fadeIn 1s ease-in-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'slide-in-up': 'slideInUp 1s ease-out forwards',
        'fade-in-out': 'fadeInOut 2s ease-in-out infinite',
        'float-bg': 'float-bg 20s ease-in-out infinite',
        'sparkle-float': 'sparkle-float 15s linear infinite',
        'slide-in-from-side': 'slideInFromSide 0.8s ease-out forwards',
        'completed-pulse': 'completedPulse 2s ease-in-out infinite',
        'checkmark-bounce': 'checkmarkBounce 0.6s ease-out',
        'typewriter': 'typewriter 0.5s ease-in-out',
        'pulse-romantic': 'pulse 2s infinite',
        'bounce-romantic': 'bounce 2s infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '25%': { transform: 'translateY(-20px) rotate(5deg)' },
          '50%': { transform: 'translateY(-10px) rotate(-3deg)' },
          '75%': { transform: 'translateY(-15px) rotate(3deg)' },
        },
        sparkleFloat: {
          '0%, 100%': { transform: 'translateY(0px) scale(1)', opacity: '0.7' },
          '25%': { transform: 'translateY(-15px) scale(1.1)', opacity: '1' },
          '50%': { transform: 'translateY(-25px) scale(0.9)', opacity: '0.8' },
          '75%': { transform: 'translateY(-10px) scale(1.05)', opacity: '0.9' },
        },
        heartbeat: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.2)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideInUp: {
          '0%': { transform: 'translateY(100px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeInOut: {
          '0%, 100%': { opacity: '0.5' },
          '50%': { opacity: '1' },
        },
        'float-bg': {
          '0%, 100%': { transform: 'translate(-50%, -50%) rotate(0deg)' },
          '33%': { transform: 'translate(-48%, -52%) rotate(1deg)' },
          '66%': { transform: 'translate(-52%, -48%) rotate(-1deg)' },
        },
        'sparkle-float': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        slideInFromSide: {
          '0%': { opacity: '0', transform: 'translateX(-50px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        completedPulse: {
          '0%, 100%': { boxShadow: '0 10px 30px rgba(255, 107, 157, 0.2), 0 0 0 0 rgba(255, 107, 157, 0.7)' },
          '50%': { boxShadow: '0 10px 30px rgba(255, 107, 157, 0.2), 0 0 0 10px rgba(255, 107, 157, 0)' },
        },
        checkmarkBounce: {
          '0%': { transform: 'scale(0)' },
          '50%': { transform: 'scale(1.2)' },
          '100%': { transform: 'scale(1)' },
        },
        typewriter: {
          '0%': { opacity: '0', transform: 'translateX(-10px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        pulse: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.1)' },
        },
        bounce: {
          '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
          '40%': { transform: 'translateY(-10px)' },
          '60%': { transform: 'translateY(-5px)' },
        },
      },
    },
  },
  plugins: [],
}
