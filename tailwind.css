@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Theme (preserved from original) */
:root {
  --primary-pink: #ff6b9d;
  --secondary-pink: #ffc3d8;
  --light-pink: #fff0f5;
  --accent-purple: #c44569;
  --soft-white: #fefefe;
  --text-dark: #2c2c2c;
  --text-light: #666;
  --gradient-bg: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 195, 216, 0.3));
  --shadow-soft: 0 10px 30px rgba(255, 107, 157, 0.2);
  --shadow-hover: 0 15px 40px rgba(255, 107, 157, 0.3);
}

[data-theme="night"] {
  --primary-pink: #ff8fab;
  --secondary-pink: #d4a5c7;
  --light-pink: #2a1f3d;
  --accent-purple: #8e44ad;
  --soft-white: #1a1a2e;
  --text-dark: #f8f8f8;
  --text-light: #cccccc;
  --gradient-bg: linear-gradient(135deg, #2c1810 0%, #3d2f47 50%, #4a3c5a 100%);
  --gradient-card: linear-gradient(145deg, rgba(42, 31, 61, 0.9), rgba(212, 165, 199, 0.1));
}

/* Base layer customizations */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Poppins', sans-serif;
    background: var(--gradient-bg);
    color: var(--text-dark);
    overflow-x: hidden;
    min-height: 100vh;
    transition: all 0.5s ease;
  }
}

/* Component layer for reusable components */
@layer components {
  .romantic-gradient {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  }
  
  .night-gradient {
    background: linear-gradient(135deg, #2c1810 0%, #3d2f47 50%, #4a3c5a 100%);
  }
  
  .card-gradient {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 195, 216, 0.3));
  }
  
  .night-card-gradient {
    background: linear-gradient(145deg, rgba(42, 31, 61, 0.9), rgba(212, 165, 199, 0.1));
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  .text-shadow-soft {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-romantic {
    text-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
  }

  .backdrop-blur-romantic {
    backdrop-filter: blur(10px) saturate(180%);
  }

  .bg-romantic-gradient {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  }

  .bg-night-gradient {
    background: linear-gradient(135deg, #2c1810 0%, #3d2f47 50%, #4a3c5a 100%);
  }

  .bg-card-gradient {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 195, 216, 0.3));
  }

  .bg-night-card-gradient {
    background: linear-gradient(145deg, rgba(42, 31, 61, 0.9), rgba(212, 165, 199, 0.1));
  }

  .shadow-romantic {
    box-shadow: 0 10px 30px rgba(255, 107, 157, 0.2);
  }

  .shadow-romantic-hover {
    box-shadow: 0 15px 40px rgba(255, 107, 157, 0.3);
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-sparkle {
    animation: sparkle 2s ease-in-out infinite;
  }

  .animate-heart-beat {
    animation: heartbeat 1.5s ease-in-out infinite;
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  /* Animation delays */
  .animation-delay-0 { animation-delay: 0ms; }
  .animation-delay-200 { animation-delay: 200ms; }
  .animation-delay-400 { animation-delay: 400ms; }
  .animation-delay-500 { animation-delay: 500ms; }
  .animation-delay-600 { animation-delay: 600ms; }
  .animation-delay-800 { animation-delay: 800ms; }
  .animation-delay-1000 { animation-delay: 1000ms; }
  .animation-delay-1200 { animation-delay: 1200ms; }
  .animation-delay-1400 { animation-delay: 1400ms; }
  .animation-delay-1500 { animation-delay: 1500ms; }
  .animation-delay-2000 { animation-delay: 2000ms; }
}
