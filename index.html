<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy 1st Monthsary, My Love 💖</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;1,400&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="./tailwind.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💖</text></svg>">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-romantic-gradient flex items-center justify-center z-50 transition-opacity duration-500">
        <div class="loading-content text-center">
            <div class="heart-loader text-6xl animate-heart-beat mb-4">💖</div>
            <p class="loading-text text-lg font-poppins text-text-dark animate-pulse">Preparing something special...</p>
        </div>
    </div>

    <!-- Animated Background -->
    <div id="animated-bg" class="fixed inset-0 -z-10 overflow-hidden"></div>

    <!-- Day/Night Toggle -->
    <div class="theme-toggle fixed top-6 right-6 z-40">
        <button id="theme-btn" class="theme-button w-14 h-14 rounded-full bg-card-gradient backdrop-blur-romantic shadow-romantic hover:shadow-romantic-hover transition-all duration-300 flex items-center justify-center text-2xl border border-white/20">
            <span class="sun">☀️</span>
            <span class="moon">🌙</span>
        </button>
    </div>

    <!-- Main Content -->
    <main id="main-content" class="opacity-0 transition-opacity duration-1000">
        <!-- Hero Section -->
        <section class="hero min-h-screen flex items-center justify-center relative px-4 py-20">
            <div class="hero-content text-center max-w-4xl mx-auto relative z-10">
                <div class="hero-decoration absolute inset-0 pointer-events-none">
                    <div class="sparkle sparkle-1 absolute top-10 left-10 text-2xl animate-sparkle">✨</div>
                    <div class="sparkle sparkle-2 absolute top-20 right-16 text-3xl animate-sparkle animation-delay-500">💫</div>
                    <div class="sparkle sparkle-3 absolute bottom-20 left-20 text-2xl animate-sparkle animation-delay-1000">⭐</div>
                    <div class="sparkle sparkle-4 absolute bottom-10 right-10 text-3xl animate-sparkle animation-delay-1500">🌟</div>
                </div>
                <h1 class="main-title font-dancing text-6xl md:text-8xl font-bold text-primary-pink mb-8 leading-tight">
                    <span class="title-line block animate-fade-in">Happy 1st Monthsary,</span>
                    <span class="title-line block animate-fade-in animation-delay-500">My Love 💖</span>
                </h1>
                <p class="hero-quote font-playfair text-xl md:text-2xl text-text-dark italic mb-8 max-w-2xl mx-auto leading-relaxed animate-fade-in animation-delay-1000">
                    "It's only been a month, but it already feels like forever — in the best way."
                </p>
                <div class="hero-date text-center animate-fade-in animation-delay-1500">
                    <span class="date-highlight block text-3xl md:text-4xl font-bold text-accent-purple mb-2">July 22, 2025</span>
                    <span class="date-subtitle text-lg text-text-light font-medium">Our Special Day</span>
                </div>
                <div class="floating-hearts flex justify-center space-x-4 mt-12 animate-fade-in animation-delay-2000">
                    <span class="heart text-2xl animate-float animation-delay-0">💕</span>
                    <span class="heart text-2xl animate-float animation-delay-200">💖</span>
                    <span class="heart text-2xl animate-float animation-delay-400">💗</span>
                    <span class="heart text-2xl animate-float animation-delay-600">💝</span>
                    <span class="heart text-2xl animate-float animation-delay-800">💘</span>
                    <span class="heart text-2xl animate-float animation-delay-1000">🌸</span>
                    <span class="heart text-2xl animate-float animation-delay-1200">🌺</span>
                    <span class="heart text-2xl animate-float animation-delay-1400">🌹</span>
                </div>
            </div>
        </section>

        <!-- The Beginning Section -->
        <section class="section beginning py-20 px-4">
            <div class="container max-w-4xl mx-auto">
                <h2 class="section-title text-4xl md:text-5xl font-dancing font-bold text-center text-primary-pink mb-16">Our Beautiful Beginning 🌸</h2>
                <div class="timeline space-y-8">
                    <div class="timeline-item flex items-center space-x-6 bg-card-gradient rounded-2xl p-6 shadow-romantic hover:shadow-romantic-hover transition-all duration-300">
                        <div class="timeline-icon text-4xl bg-primary-pink/20 rounded-full w-16 h-16 flex items-center justify-center">👋</div>
                        <div class="timeline-content flex-1">
                            <h3 class="text-xl font-semibold text-accent-purple mb-2">August 22, 2023</h3>
                            <p class="text-text-dark">The day we first met and our beautiful journey began</p>
                        </div>
                    </div>
                    <div class="timeline-item flex items-center space-x-6 bg-card-gradient rounded-2xl p-6 shadow-romantic hover:shadow-romantic-hover transition-all duration-300">
                        <div class="timeline-icon text-4xl bg-secondary-pink/30 rounded-full w-16 h-16 flex items-center justify-center">💬</div>
                        <div class="timeline-content flex-1">
                            <h3 class="text-xl font-semibold text-accent-purple mb-2">First Deep Conversation</h3>
                            <p class="text-text-dark">When we talked for hours and realized how much we had in common</p>
                        </div>
                    </div>
                    <div class="timeline-item flex items-center space-x-6 bg-card-gradient rounded-2xl p-6 shadow-romantic hover:shadow-romantic-hover transition-all duration-300">
                        <div class="timeline-icon text-4xl bg-romantic-200/30 rounded-full w-16 h-16 flex items-center justify-center">📱</div>
                        <div class="timeline-content flex-1">
                            <h3 class="text-xl font-semibold text-accent-purple mb-2">First Video Call</h3>
                            <p class="text-text-dark">Seeing your beautiful smile light up my screen for the first time</p>
                        </div>
                    </div>
                    <div class="timeline-item flex items-center space-x-6 bg-card-gradient rounded-2xl p-6 shadow-romantic hover:shadow-romantic-hover transition-all duration-300">
                        <div class="timeline-icon text-4xl bg-primary-pink/20 rounded-full w-16 h-16 flex items-center justify-center">💕</div>
                        <div class="timeline-content flex-1">
                            <h3 class="text-xl font-semibold text-accent-purple mb-2">June 22, 2025</h3>
                            <p class="text-text-dark">When we officially became a couple and started our love story</p>
                        </div>
                    </div>
                    <div class="timeline-item flex items-center space-x-6 bg-card-gradient rounded-2xl p-6 shadow-romantic hover:shadow-romantic-hover transition-all duration-300">
                        <div class="timeline-icon text-4xl bg-accent-purple/20 rounded-full w-16 h-16 flex items-center justify-center">🎂</div>
                        <div class="timeline-content flex-1">
                            <h3 class="text-xl font-semibold text-accent-purple mb-2">July 22, 2025</h3>
                            <p class="text-text-dark">Our first monthsary - celebrating one month of pure happiness</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Favorite Things Section -->
        <section class="section favorites py-20 px-4 bg-light-pink/30">
            <div class="container max-w-6xl mx-auto">
                <h2 class="section-title text-4xl md:text-5xl font-dancing font-bold text-center text-primary-pink mb-16">My Favorite Things About You 💝</h2>
                <div class="favorites-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="favorite-card bg-card-gradient rounded-2xl p-8 text-center shadow-romantic hover:shadow-romantic-hover transition-all duration-300 hover:scale-105">
                        <div class="card-icon text-5xl mb-4">😊</div>
                        <h3 class="text-xl font-semibold text-accent-purple mb-3">Your Smile</h3>
                        <p class="text-text-dark leading-relaxed">It lights up my entire world and makes everything better</p>
                    </div>
                    <div class="favorite-card bg-card-gradient rounded-2xl p-8 text-center shadow-romantic hover:shadow-romantic-hover transition-all duration-300 hover:scale-105">
                        <div class="card-icon text-5xl mb-4">😂</div>
                        <h3 class="text-xl font-semibold text-accent-purple mb-3">Your Laugh</h3>
                        <p class="text-text-dark leading-relaxed">The most beautiful sound that never fails to make me happy</p>
                    </div>
                    <div class="favorite-card bg-card-gradient rounded-2xl p-8 text-center shadow-romantic hover:shadow-romantic-hover transition-all duration-300 hover:scale-105">
                        <div class="card-icon text-5xl mb-4">🤗</div>
                        <h3 class="text-xl font-semibold text-accent-purple mb-3">Your Hugs</h3>
                        <p class="text-text-dark leading-relaxed">Even through the screen, they feel like home</p>
                    </div>
                    <div class="favorite-card bg-card-gradient rounded-2xl p-8 text-center shadow-romantic hover:shadow-romantic-hover transition-all duration-300 hover:scale-105">
                        <div class="card-icon text-5xl mb-4">😴</div>
                        <h3 class="text-xl font-semibold text-accent-purple mb-3">Your Sleepy Voice</h3>
                        <p class="text-text-dark leading-relaxed">So soft and sweet, it melts my heart every time</p>
                    </div>
                    <div class="favorite-card bg-card-gradient rounded-2xl p-8 text-center shadow-romantic hover:shadow-romantic-hover transition-all duration-300 hover:scale-105">
                        <div class="card-icon text-5xl mb-4">🔥</div>
                        <h3 class="text-xl font-semibold text-accent-purple mb-3">Your Passion</h3>
                        <p class="text-text-dark leading-relaxed">The way you light up when talking about things you love</p>
                    </div>
                    <div class="favorite-card bg-card-gradient rounded-2xl p-8 text-center shadow-romantic hover:shadow-romantic-hover transition-all duration-300 hover:scale-105">
                        <div class="card-icon text-5xl mb-4">💖</div>
                        <h3 class="text-xl font-semibold text-accent-purple mb-3">Your Heart</h3>
                        <p class="text-text-dark leading-relaxed">So kind, caring, and beautiful in every way</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Roadmap Section -->
        <section class="section roadmap py-20 px-4">
            <div class="container max-w-6xl mx-auto">
                <h2 class="section-title text-4xl md:text-5xl font-dancing font-bold text-center text-primary-pink mb-4">Roadmap 🗺️</h2>
                <p class="roadmap-subtitle text-lg text-text-light text-center mb-16">Lists of accomplished goals</p>
                <div class="roadmap-timeline space-y-16">
                    <!-- Item 1: Picture Left, Text Right -->
                    <div class="roadmap-item completed grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                        <div class="roadmap-photo lg:col-span-4">
                            <div class="photo-frame relative bg-card-gradient rounded-2xl p-4 shadow-romantic">
                                <img src="https://images.unsplash.com/photo-1530841344095-b2893194affe?w=300&h=200&fit=crop&crop=center" alt="BBQ Party" class="roadmap-image w-full h-48 object-cover rounded-xl">
                                <div class="photo-caption absolute bottom-2 left-2 right-2 bg-black/50 text-white text-sm px-3 py-1 rounded-lg backdrop-blur-sm">That first hello</div>
                            </div>
                        </div>
                        <div class="roadmap-marker lg:col-span-1 flex justify-center">
                            <div class="relative">
                                <div class="roadmap-icon text-3xl bg-primary-pink/20 rounded-full w-16 h-16 flex items-center justify-center">🎉</div>
                                <div class="completion-badge absolute -top-2 -right-2 bg-green-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold">✓</div>
                            </div>
                        </div>
                        <div class="roadmap-content lg:col-span-7 bg-card-gradient rounded-2xl p-6 shadow-romantic">
                            <h3 class="text-2xl font-semibold text-accent-purple mb-3">When We First Met</h3>
                            <p class="text-text-dark leading-relaxed">A random introduction at a mutual friend's BBQ. Just a quick hello—but something lingered in the air.</p>
                        </div>
                    </div>

                    <!-- Item 2: Text Left, Picture Right -->
                    <div class="roadmap-item completed reverse grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                        <div class="roadmap-content lg:col-span-7 bg-card-gradient rounded-2xl p-6 shadow-romantic order-2 lg:order-1">
                            <h3 class="text-2xl font-semibold text-accent-purple mb-3">First Real Hangout</h3>
                            <p class="text-text-dark leading-relaxed">Grabbed pizza after a long walk in the city. No fancy plans—just conversation, comfort, and a hint of something more.</p>
                        </div>
                        <div class="roadmap-marker lg:col-span-1 flex justify-center order-1 lg:order-2">
                            <div class="relative">
                                <div class="roadmap-icon text-3xl bg-secondary-pink/30 rounded-full w-16 h-16 flex items-center justify-center">🍕</div>
                                <div class="completion-badge absolute -top-2 -right-2 bg-green-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold">✓</div>
                            </div>
                        </div>
                        <div class="roadmap-photo lg:col-span-4 order-3">
                            <div class="photo-frame relative bg-card-gradient rounded-2xl p-4 shadow-romantic">
                                <img src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=200&fit=crop&crop=center" alt="City Walk and Pizza" class="roadmap-image w-full h-48 object-cover rounded-xl">
                                <div class="photo-caption absolute bottom-2 left-2 right-2 bg-black/50 text-white text-sm px-3 py-1 rounded-lg backdrop-blur-sm">Perfect first date</div>
                            </div>
                        </div>
                    </div>

                    <!-- Item 3: Picture Left, Text Right -->
                    <div class="roadmap-item completed grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                        <div class="roadmap-photo lg:col-span-4">
                            <div class="photo-frame relative bg-card-gradient rounded-2xl p-4 shadow-romantic">
                                <img src="https://images.unsplash.com/photo-1516726817505-f5ed825624d8?w=300&h=200&fit=crop&crop=center" alt="Late Night Call" class="roadmap-image w-full h-48 object-cover rounded-xl">
                                <div class="photo-caption absolute bottom-2 left-2 right-2 bg-black/50 text-white text-sm px-3 py-1 rounded-lg backdrop-blur-sm">Time stood still</div>
                            </div>
                        </div>
                        <div class="roadmap-marker lg:col-span-1 flex justify-center">
                            <div class="relative">
                                <div class="roadmap-icon text-3xl bg-romantic-200/30 rounded-full w-16 h-16 flex items-center justify-center">📞</div>
                                <div class="completion-badge absolute -top-2 -right-2 bg-green-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold">✓</div>
                            </div>
                        </div>
                        <div class="roadmap-content lg:col-span-7 bg-card-gradient rounded-2xl p-6 shadow-romantic">
                            <h3 class="text-2xl font-semibold text-accent-purple mb-3">The Night We Talked for Hours</h3>
                            <p class="text-text-dark leading-relaxed">What started as a quick chat turned into a 6-hour call. Everything just flowed—like we'd known each other forever.</p>
                        </div>
                    </div>

                    <!-- Item 4: Text Left, Picture Right -->
                    <div class="roadmap-item completed reverse grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                        <div class="roadmap-content lg:col-span-7 bg-card-gradient rounded-2xl p-6 shadow-romantic order-2 lg:order-1">
                            <h3 class="text-2xl font-semibold text-accent-purple mb-3">Our First Adventure</h3>
                            <p class="text-text-dark leading-relaxed">Tried an amusement park together—screams on the roller coaster, laughter in the rain, and matching souvenir shirts.</p>
                        </div>
                        <div class="roadmap-marker lg:col-span-1 flex justify-center order-1 lg:order-2">
                            <div class="relative">
                                <div class="roadmap-icon text-3xl bg-primary-pink/20 rounded-full w-16 h-16 flex items-center justify-center">🎢</div>
                                <div class="completion-badge absolute -top-2 -right-2 bg-green-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold">✓</div>
                            </div>
                        </div>
                        <div class="roadmap-photo lg:col-span-4 order-3">
                            <div class="photo-frame relative bg-card-gradient rounded-2xl p-4 shadow-romantic">
                                <img src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=200&fit=crop&crop=center" alt="Amusement Park" class="roadmap-image w-full h-48 object-cover rounded-xl">
                                <div class="photo-caption absolute bottom-2 left-2 right-2 bg-black/50 text-white text-sm px-3 py-1 rounded-lg backdrop-blur-sm">Screams and laughter</div>
                            </div>
                        </div>
                    </div>

                    <!-- Item 5: Picture Left, Text Right -->
                    <div class="roadmap-item completed grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                        <div class="roadmap-photo lg:col-span-4">
                            <div class="photo-frame relative bg-card-gradient rounded-2xl p-4 shadow-romantic">
                                <img src="https://images.unsplash.com/photo-1560440021-33f9b867899d?w=300&h=200&fit=crop&crop=center" alt="Moving In" class="roadmap-image w-full h-48 object-cover rounded-xl">
                                <div class="photo-caption absolute bottom-2 left-2 right-2 bg-black/50 text-white text-sm px-3 py-1 rounded-lg backdrop-blur-sm">Our first home</div>
                            </div>
                        </div>
                        <div class="roadmap-marker lg:col-span-1 flex justify-center">
                            <div class="relative">
                                <div class="roadmap-icon text-3xl bg-accent-purple/20 rounded-full w-16 h-16 flex items-center justify-center">🏠</div>
                                <div class="completion-badge absolute -top-2 -right-2 bg-green-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold">✓</div>
                            </div>
                        </div>
                        <div class="roadmap-content lg:col-span-7 bg-card-gradient rounded-2xl p-6 shadow-romantic">
                            <h3 class="text-2xl font-semibold text-accent-purple mb-3">The Move-In Moonlight</h3>
                            <p class="text-text-dark leading-relaxed">Moved in with just a mattress and a coffee machine. Late-night decorating, pizza on the floor, and endless smiles.</p>
                        </div>
                    </div>

                    <!-- Item 6: Text Left, Picture Right -->
                    <div class="roadmap-item completed reverse grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                        <div class="roadmap-content lg:col-span-7 bg-card-gradient rounded-2xl p-6 shadow-romantic order-2 lg:order-1">
                            <h3 class="text-2xl font-semibold text-accent-purple mb-3">When We Wrote Our Vows</h3>
                            <p class="text-text-dark leading-relaxed">Sitting side by side, writing down promises that came from the heart. Every word was a memory in the making.</p>
                        </div>
                        <div class="roadmap-marker lg:col-span-1 flex justify-center order-1 lg:order-2">
                            <div class="relative">
                                <div class="roadmap-icon text-3xl bg-secondary-pink/30 rounded-full w-16 h-16 flex items-center justify-center">💍</div>
                                <div class="completion-badge absolute -top-2 -right-2 bg-green-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold">✓</div>
                            </div>
                        </div>
                        <div class="roadmap-photo lg:col-span-4 order-3">
                            <div class="photo-frame relative bg-card-gradient rounded-2xl p-4 shadow-romantic">
                                <img src="https://images.unsplash.com/photo-1519741497674-611481863552?w=300&h=200&fit=crop&crop=center" alt="Writing Vows" class="roadmap-image w-full h-48 object-cover rounded-xl">
                                <div class="photo-caption absolute bottom-2 left-2 right-2 bg-black/50 text-white text-sm px-3 py-1 rounded-lg backdrop-blur-sm">Promises from the heart</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Love Letter from a Coder Section -->
        <section class="section code-letter">
            <div class="container">
                <h2 class="section-title">A Love Letter from a Coder 💻</h2>
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-buttons">
                            <span class="btn red"></span>
                            <span class="btn yellow"></span>
                            <span class="btn green"></span>
                        </div>
                        <div class="terminal-title">love_letter.js</div>
                    </div>
                    <div class="terminal-body">
                        <div id="code-content">
                            <div class="code-line"><span class="comment">// Initializing love variables...</span></div>
                            <div class="code-line"><span class="keyword">const</span> <span class="variable">myHeart</span> = <span class="string">"yours forever"</span>;</div>
                            <div class="code-line"><span class="keyword">let</span> <span class="variable">loveLevel</span> = <span class="number">Infinity</span>;</div>
                            <div class="code-line"><span class="keyword">let</span> <span class="variable">daysWithYou</span> = <span class="number">30</span>;</div>
                            <div class="code-line"></div>
                            <div class="code-line"><span class="keyword">function</span> <span class="function">expressLove</span>() {</div>
                            <div class="code-line">&nbsp;&nbsp;<span class="keyword">while</span> (<span class="variable">daysWithYou</span> < <span class="number">Infinity</span>) {</div>
                            <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;<span class="variable">loveLevel</span>++;</div>
                            <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;<span class="variable">daysWithYou</span>++;</div>
                            <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;console.log(<span class="string">"I love you more each day"</span>);</div>
                            <div class="code-line">&nbsp;&nbsp;}</div>
                            <div class="code-line">}</div>
                            <div class="code-line"></div>
                            <div class="code-line"><span class="comment">// Running the love function...</span></div>
                            <div class="code-line"><span class="function">expressLove</span>();</div>
                        </div>
                        <div id="output-content" style="display: none;">
                            <div class="output-line">💖 Compiling love...</div>
                            <div class="output-line">💕 Loading memories...</div>
                            <div class="output-line">💗 Processing feelings...</div>
                            <div class="output-line"></div>
                            <div class="output-line">Output:</div>
                            <div class="output-line">"My dearest love,"</div>
                            <div class="output-line">"In just one month, you've become my everything."</div>
                            <div class="output-line">"Every line of code I write, every bug I fix,"</div>
                            <div class="output-line">"reminds me that the most beautiful thing"</div>
                            <div class="output-line">"I've ever created is this love we share."</div>
                            <div class="output-line">"You're not just my girlfriend,"</div>
                            <div class="output-line">"you're my favorite feature, my best function,"</div>
                            <div class="output-line">"and the perfect solution to my heart's algorithm."</div>
                            <div class="output-line">"I love you beyond syntax errors and infinite loops."</div>
                            <div class="output-line">"Forever yours, Your Coding Boyfriend 💖"</div>
                        </div>
                    </div>
                </div>
                <button id="run-code" class="run-button">Run Code ▶️</button>
            </div>
        </section>

        <!-- Our Song Section -->
        <section class="section music">
            <div class="container">
                <h2 class="section-title">Our Song 🎵</h2>
                <div class="music-player">
                    <div class="music-info">
                        <h3>The song that reminds me of you</h3>
                        <p>Every time I hear this, I think of us and smile</p>
                    </div>
                    <div class="spotify-embed">
                        <!-- Replace with your actual Spotify embed or YouTube link -->
                        <iframe style="border-radius:12px" src="https://open.spotify.com/embed/track/4uLU6hMCjMI75M1A2tKUQC?utm_source=generator" width="100%" height="152" frameBorder="0" allowfullscreen="" allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" loading="lazy"></iframe>
                    </div>
                </div>
            </div>
        </section>

        <!-- Promises Section -->
        <section class="section promises">
            <div class="container">
                <h2 class="section-title">My Promises to You 💍</h2>
                <div class="promises-list">
                    <div class="promise-item">
                        <span class="promise-icon">👂</span>
                        <p>I promise to always listen to you with my whole heart</p>
                    </div>
                    <div class="promise-item">
                        <span class="promise-icon">💪</span>
                        <p>I promise to never stop trying to be better for you</p>
                    </div>
                    <div class="promise-item">
                        <span class="promise-icon">🤝</span>
                        <p>I promise to always show up when you need me</p>
                    </div>
                    <div class="promise-item">
                        <span class="promise-icon">😊</span>
                        <p>I promise to make you smile every single day</p>
                    </div>
                    <div class="promise-item">
                        <span class="promise-icon">🌟</span>
                        <p>I promise to support your dreams and celebrate your wins</p>
                    </div>
                    <div class="promise-item">
                        <span class="promise-icon">💖</span>
                        <p>I promise to love you more with each passing day</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Time Together Section -->
        <section class="section time-together">
            <div class="container">
                <h2 class="section-title">Our Time Together 💕</h2>
                <div class="love-stats">
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <span id="total-days" class="stat-number">0</span>
                            <span class="stat-label">Days Together</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏰</div>
                        <div class="stat-content">
                            <span id="total-hours" class="stat-number">0</span>
                            <span class="stat-label">Hours of Love</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💖</div>
                        <div class="stat-content">
                            <span id="total-minutes" class="stat-number">0</span>
                            <span class="stat-label">Minutes of Joy</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✨</div>
                        <div class="stat-content">
                            <span id="total-seconds" class="stat-number">0</span>
                            <span class="stat-label">Seconds of Magic</span>
                        </div>
                    </div>
                </div>
                <div class="milestone-message">
                    <p class="milestone-text">
                        Since August 22, 2023, every moment with you has been a blessing 💝
                    </p>
                </div>
            </div>
        </section>

        <!-- Easter Egg Section -->
        <div class="floating-surprise">
            <div id="secret-heart" class="secret-heart">💖</div>
        </div>

        <!-- Secret Message Modal -->
        <div id="secret-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Secret Message 💕</h2>
                <p class="secret-message">
                    "This is just Month 1 — wait 'til you see what Month 2 brings. 
                    I have so many more surprises, adventures, and love to share with you. 
                    Thank you for being the most amazing girlfriend ever. 
                    Here's to forever and always! 💖✨"
                </p>
                <button id="fireworks-btn" class="surprise-button">Launch Celebration! 🎆</button>
            </div>
        </div>

        <!-- Download Section -->
        <section class="section download">
            <div class="container">
                <h2 class="section-title">Keep This Forever 💾</h2>
                <button id="download-pdf" class="download-button">
                    Download Love Letter PDF 📄💖
                </button>
            </div>
        </section>
    </main>

    <script src="./main.js"></script>
</body>
</html>
