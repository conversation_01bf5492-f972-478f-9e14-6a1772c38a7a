# 💖 Happy 1st Monthsary Website 💖

A beautiful, romantic, and fully responsive website created to celebrate your first monthsary! This website is designed with love, featuring soft pastels, romantic animations, and heartfelt content.

## ✨ Features

### 🌸 Design & Aesthetics
- Soft pink and pastel color scheme
- Romantic fonts (Dancing Script, Playfair Display, Poppins)
- Smooth animations and transitions
- Particle background effects
- Day/Night theme toggle
- Fully responsive design

### 💕 Sections Included
1. **Hero Section** - Beautiful title with floating hearts animation
2. **Our Beginning** - Timeline of your love story
3. **Favorite Things** - Grid of things you love about her
4. **Photo Gallery** - Polaroid-style photo frames
5. **Love Letter from a Coder** - Interactive terminal with code-to-love transformation
6. **Our Song** - Embedded music player
7. **Promises** - List of romantic promises
8. **Countdown** - Timer to your 2nd monthsary
9. **Easter Egg** - Hidden surprise with fireworks!
10. **Download** - Save the love letter as a text file

### 🎯 Interactive Features
- Loading screen with heart animation
- Floating particle effects
- Smooth scrolling animations
- Code terminal that transforms into a love letter
- Clickable secret heart with surprise message
- Fireworks celebration
- Theme toggle (Day/Night mode)
- PDF/Text download functionality
- Countdown timer

## 🚀 How to Use

### Option 1: Direct File Opening
Simply double-click on `index.html` to open it in your browser.

### Option 2: Local Server (Recommended)
1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Open the provided localhost URL

### Option 3: Python Server
```bash
python -m http.server 8000
```
Then visit `http://localhost:8000`

## 🎨 Customization

### Personal Content
- Update the timeline events in the "Our Beginning" section
- Modify the "Favorite Things" cards with your specific reasons
- Replace photo placeholders with actual photos
- Update the Spotify embed with your actual song
- Customize the promises list
- Set the correct date for your 2nd monthsary in `main.js`

### Styling
- Colors can be modified in the CSS variables at the top of `style.css`
- Fonts can be changed by updating the Google Fonts imports
- Animation speeds and effects can be adjusted in the CSS and JavaScript

## 📱 Mobile Responsive
The website is fully responsive and looks beautiful on:
- Desktop computers
- Tablets
- Mobile phones

## 🎁 Special Features

### Easter Egg
Click the floating heart in the bottom-right corner for a special surprise!

### Theme Toggle
Switch between day and night themes using the button in the top-right corner.

### Download Feature
Save a text version of the love letter to keep forever.

## 💻 Technical Details
- Built with vanilla HTML, CSS, and JavaScript
- Uses Particles.js for background effects
- Canvas Confetti for fireworks animation
- Google Fonts for beautiful typography
- No backend required - fully client-side

## 🌟 Hosting Options
This website can be easily hosted on:
- GitHub Pages
- Netlify
- Vercel
- Any static hosting service

## 💖 Made with Love
This website was crafted with love and attention to detail. Every animation, every color choice, and every word was chosen to create something special and memorable.

Enjoy your 1st monthsary celebration! 🎉💕
